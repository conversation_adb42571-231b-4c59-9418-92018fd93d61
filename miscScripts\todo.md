animeTitlesParse replaces "0 with "o" ???
normalized name isnt always normalized
    "id": "27292",
    "songName": "Sakura Sakura Saku ~<PERSON>o <PERSON> o <PERSON><PERSON> to Onaji de~ ~Acoustic&hidamarble version~",
    "normalizedName": "sakura sakura saku ~ano hi kimi o matsu sora to onaji de~ ~acousticandhidamarble version~",
    "displayName": "Sakura Sakura Saku ~<PERSON>o <PERSON> o <PERSON> to Onaji de~ ~Acoustic&hidamarble version~",
    "string_length": 87,
    "normalized_length": 89

normalized name should be the same length, use it to highlight